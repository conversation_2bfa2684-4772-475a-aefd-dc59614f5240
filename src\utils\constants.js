/**
 * 系统常量定义
 *
 * 定义系统中使用的各种常量，确保前后端一致性。
 */

/**
 * 违规记录状态常量
 */
export const ViolationStatus = {
  // 前端状态值
  PENDING: 0,      // 待审核
  PROCESSED: 1,    // 已处理
  APPEALING: 2,    // 申诉中
  REVOKED: 3,      // 已撤销

  // 显示文本
  TEXT_PENDING: '待审核',
  TEXT_PROCESSED: '已处理',
  TEXT_APPEALING: '申诉中',
  TEXT_REVOKED: '已撤销',

  // 状态类型（用于显示不同颜色的标签）
  TYPE_PENDING: 'info',
  TYPE_PROCESSED: 'success',
  TYPE_APPEALING: 'warning',
  TYPE_REVOKED: 'danger',

  // 状态图标
  ICON_PENDING: 'el-icon-time',
  ICON_PROCESSED: 'el-icon-check',
  ICON_APPEALING: 'el-icon-warning',
  ICON_REVOKED: 'el-icon-circle-close',

  /**
   * 获取状态文本
   * @param {Number} status - 状态值
   * @returns {String} - 状态文本
   */
  getText(status) {
    const textMap = {
      [this.PENDING]: this.TEXT_PENDING,
      [this.PROCESSED]: this.TEXT_PROCESSED,
      [this.APPEALING]: this.TEXT_APPEALING,
      [this.REVOKED]: this.TEXT_REVOKED
    }
    return textMap[status] || '未知状态'
  },

  /**
   * 获取状态类型
   * @param {Number} status - 状态值
   * @returns {String} - 状态类型
   */
  getType(status) {
    const typeMap = {
      [this.PENDING]: this.TYPE_PENDING,
      [this.PROCESSED]: this.TYPE_PROCESSED,
      [this.APPEALING]: this.TYPE_APPEALING,
      [this.REVOKED]: this.TYPE_REVOKED
    }
    return typeMap[status] || 'info'
  },

  /**
   * 获取状态图标
   * @param {Number} status - 状态值
   * @returns {String} - 状态图标
   */
  getIcon(status) {
    const iconMap = {
      [this.PENDING]: this.ICON_PENDING,
      [this.PROCESSED]: this.ICON_PROCESSED,
      [this.APPEALING]: this.ICON_APPEALING,
      [this.REVOKED]: this.ICON_REVOKED
    }
    return iconMap[status] || 'el-icon-info'
  }
}

/**
 * 申诉状态常量
 */
export const AppealStatus = {
  // 前端状态值
  PENDING: 0,      // 待审核
  APPROVED: 1,     // 已通过
  REJECTED: 2,     // 未通过

  // 显示文本
  TEXT_PENDING: '待审核',
  TEXT_APPROVED: '已通过',
  TEXT_REJECTED: '未通过',

  // 状态类型（用于显示不同颜色的标签）
  TYPE_PENDING: 'warning',
  TYPE_APPROVED: 'success',
  TYPE_REJECTED: 'danger',

  // 状态图标
  ICON_PENDING: 'el-icon-time',
  ICON_APPROVED: 'el-icon-circle-check',
  ICON_REJECTED: 'el-icon-circle-close',

  /**
   * 获取状态文本
   * @param {Number} status - 状态值
   * @returns {String} - 状态文本
   */
  getText(status) {
    const textMap = {
      [this.PENDING]: this.TEXT_PENDING,
      [this.APPROVED]: this.TEXT_APPROVED,
      [this.REJECTED]: this.TEXT_REJECTED
    }
    return textMap[status] || '未知状态'
  },

  /**
   * 获取状态类型
   * @param {Number} status - 状态值
   * @returns {String} - 状态类型
   */
  getType(status) {
    const typeMap = {
      [this.PENDING]: this.TYPE_PENDING,
      [this.APPROVED]: this.TYPE_APPROVED,
      [this.REJECTED]: this.TYPE_REJECTED
    }
    return typeMap[status] || 'info'
  },

  /**
   * 获取状态图标
   * @param {Number} status - 状态值
   * @returns {String} - 状态图标
   */
  getIcon(status) {
    const iconMap = {
      [this.PENDING]: this.ICON_PENDING,
      [this.APPROVED]: this.ICON_APPROVED,
      [this.REJECTED]: this.ICON_REJECTED
    }
    return iconMap[status] || 'el-icon-info'
  }
}

/**
 * 车辆状态常量
 */
export const VehicleStatus = {
  // 前端状态值
  AVAILABLE: 'available',  // 车辆可用状态
  DISABLED: 'unavailable', // 车辆禁用状态

  // 后端状态值
  BACKEND_AVAILABLE: '可用',  // 后端可用状态
  BACKEND_DISABLED: '废弃',   // 后端禁用状态

  // 显示文本
  TEXT_AVAILABLE: '正常',  // 可用状态显示文本
  TEXT_DISABLED: '停用',   // 禁用状态显示文本

  // 状态映射（前端 -> 后端）
  FRONTEND_TO_BACKEND: {
    'available': '可用',
    'unavailable': '废弃',
    '正常': '可用',
    '停用': '废弃'
  },

  // 状态映射（后端 -> 前端）
  BACKEND_TO_FRONTEND: {
    '可用': 'available',
    '废弃': 'unavailable'
  },

  // 状态映射（前端 -> 显示文本）
  FRONTEND_TO_TEXT: {
    'available': '正常',
    'unavailable': '停用'
  },

  // 状态映射（后端 -> 显示文本）
  BACKEND_TO_TEXT: {
    '可用': '正常',
    '废弃': '停用'
  },

  /**
   * 将前端状态值转换为后端状态值
   * @param {String} frontendStatus - 前端状态值
   * @returns {String} - 后端状态值
   */
  convertToBackend(frontendStatus) {
    return this.FRONTEND_TO_BACKEND[frontendStatus] || this.BACKEND_AVAILABLE
  },

  /**
   * 将后端状态值转换为前端状态值
   * @param {String} backendStatus - 后端状态值
   * @returns {String} - 前端状态值
   */
  convertToFrontend(backendStatus) {
    return this.BACKEND_TO_FRONTEND[backendStatus] || this.AVAILABLE
  },

  /**
   * 获取状态的显示文本
   * @param {String} status - 状态值
   * @param {Boolean} isBackend - 是否为后端状态值
   * @returns {String} - 显示文本
   */
  getText(status, isBackend = false) {
    if (isBackend) {
      return this.BACKEND_TO_TEXT[status] || this.TEXT_AVAILABLE
    } else {
      return this.FRONTEND_TO_TEXT[status] || this.TEXT_AVAILABLE
    }
  },

  /**
   * 检查状态是否为可用状态
   * @param {String} status - 状态值
   * @param {Boolean} isBackend - 是否为后端状态值
   * @returns {Boolean} - 是否为可用状态
   */
  isAvailable(status, isBackend = false) {
    if (isBackend) {
      return status === this.BACKEND_AVAILABLE
    } else {
      return status === this.AVAILABLE
    }
  },

  /**
   * 检查状态是否为禁用状态
   * @param {String} status - 状态值
   * @param {Boolean} isBackend - 是否为后端状态值
   * @returns {Boolean} - 是否为禁用状态
   */
  isDisabled(status, isBackend = false) {
    if (isBackend) {
      return status === this.BACKEND_DISABLED
    } else {
      return status === this.DISABLED
    }
  }
}

/**
 * 校区类型常量
 */
export const CampusTypes = {
  // 校区选项
  OPTIONS: [
    { label: '北校区', value: '北校区' },
    { label: '南校区', value: '南校区' },
    { label: '东校区', value: '东校区' },
    { label: '西校区', value: '西校区' },
    { label: '主校区', value: '主校区' }
  ],

  // 校区值
  NORTH: '北校区',
  SOUTH: '南校区',
  EAST: '东校区',
  WEST: '西校区',
  MAIN: '主校区',

  // 获取所有校区值
  getAllValues() {
    return this.OPTIONS.map(option => option.value)
  },

  // 获取校区标签
  getLabel(value) {
    const option = this.OPTIONS.find(opt => opt.value === value)
    return option ? option.label : value
  }
}

/**
 * 区域类型常量
 */
export const AreaTypes = {
  // 区域选项
  OPTIONS: [
    { label: '教学区', value: '教学区' },
    { label: '宿舍区', value: '宿舍区' },
    { label: '图书馆区', value: '图书馆区' },
    { label: '食堂区', value: '食堂区' },
    { label: '体育区', value: '体育区' },
    { label: '体育馆区', value: '体育馆区' },
    { label: '教师公寓区', value: '教师公寓区' },
    { label: '行政区', value: '行政区' },
    { label: '实验区', value: '实验区' }
  ],

  // 区域值
  TEACHING: '教学区',
  DORMITORY: '宿舍区',
  LIBRARY: '图书馆区',
  CANTEEN: '食堂区',
  SPORTS: '体育区',
  GYMNASIUM: '体育馆区',
  FACULTY_APARTMENT: '教师公寓区',
  ADMINISTRATION: '行政区',
  LABORATORY: '实验区',

  // 获取所有区域值
  getAllValues() {
    return this.OPTIONS.map(option => option.value)
  },

  // 获取区域标签
  getLabel(value) {
    const option = this.OPTIONS.find(opt => opt.value === value)
    return option ? option.label : value
  }
}

/**
 * 停车场状态常量
 */
export const ParkingLotStatus = {
  // 前端状态值
  ACTIVE: 'active',      // 停车场激活状态
  INACTIVE: 'inactive',  // 停车场未激活状态

  // 后端状态值
  BACKEND_ACTIVE: 1,    // 后端激活状态
  BACKEND_INACTIVE: 0,  // 后端未激活状态

  // 显示文本
  TEXT_ACTIVE: '正常',    // 激活状态显示文本
  TEXT_INACTIVE: '停用'   // 未激活状态显示文本
}

/**
 * 车位类型常量
 */
export const ParkingSpaceTypes = {
  // 车位类型选项
  OPTIONS: [
    { label: '普通车位', value: 1, icon: 'el-icon-location', color: '#409EFF' },
    { label: '残疾人车位', value: 2, icon: 'el-icon-user', color: '#E6A23C' },
    { label: '充电车位', value: 3, icon: 'el-icon-lightning', color: '#67C23A' }
  ],

  // 车位类型值
  NORMAL: 1,      // 普通车位
  DISABLED: 2,    // 残疾人车位
  CHARGING: 3,    // 充电车位

  // 显示文本
  TEXT_NORMAL: '普通车位',
  TEXT_DISABLED: '残疾人车位',
  TEXT_CHARGING: '充电车位',

  // 车位编号前缀
  PREFIX_NORMAL: 'N',     // 普通车位前缀
  PREFIX_DISABLED: 'D',   // 残疾人车位前缀
  PREFIX_CHARGING: 'C',   // 充电车位前缀

  // 获取车位类型标签
  getLabel(type) {
    const option = this.OPTIONS.find(opt => opt.value === type)
    return option ? option.label : '未知类型'
  },

  // 获取车位类型图标
  getIcon(type) {
    const option = this.OPTIONS.find(opt => opt.value === type)
    return option ? option.icon : 'el-icon-location'
  },

  // 获取车位类型颜色
  getColor(type) {
    const option = this.OPTIONS.find(opt => opt.value === type)
    return option ? option.color : '#909399'
  },

  // 获取车位编号前缀
  getPrefix(type) {
    switch (type) {
      case this.NORMAL: return this.PREFIX_NORMAL
      case this.DISABLED: return this.PREFIX_DISABLED
      case this.CHARGING: return this.PREFIX_CHARGING
      default: return 'U' // Unknown
    }
  },

  // 根据前缀获取车位类型
  getTypeByPrefix(prefix) {
    switch (prefix.toUpperCase()) {
      case this.PREFIX_NORMAL: return this.NORMAL
      case this.PREFIX_DISABLED: return this.DISABLED
      case this.PREFIX_CHARGING: return this.CHARGING
      default: return this.NORMAL
    }
  }
}

/**
 * 车位状态常量
 */
export const ParkingSpaceStatus = {
  // 前端状态值
  AVAILABLE: 'available',      // 车位空闲状态
  OCCUPIED: 'occupied',        // 车位已占用状态
  MAINTENANCE: 'maintenance',  // 车位维护中状态
  DISABLED: 'disabled',        // 车位禁用状态

  // 后端状态值
  BACKEND_AVAILABLE: 0,      // 后端空闲状态
  BACKEND_OCCUPIED: 1,       // 后端已占用状态
  BACKEND_MAINTENANCE: 2,    // 后端维护中状态
  BACKEND_DISABLED: 3,       // 后端禁用状态

  // 显示文本
  TEXT_AVAILABLE: '空闲',    // 空闲状态显示文本
  TEXT_OCCUPIED: '已占用',   // 已占用状态显示文本
  TEXT_MAINTENANCE: '维护中', // 维护中状态显示文本
  TEXT_DISABLED: '禁用'      // 禁用状态显示文本
}

/**
 * 停车记录状态常量
 */
export const ParkingRecordStatus = {
  // 前端状态值
  ACTIVE: 'active',        // 停车记录进行中状态
  COMPLETED: 'completed',  // 停车记录已完成状态
  ABNORMAL: 'abnormal',    // 停车记录异常状态

  // 后端状态值
  BACKEND_ACTIVE: 0,      // 后端进行中状态
  BACKEND_COMPLETED: 1,   // 后端已完成状态
  BACKEND_ABNORMAL: 2,    // 后端异常状态

  // 显示文本
  TEXT_ACTIVE: '进行中',    // 进行中状态显示文本
  TEXT_COMPLETED: '已完成', // 已完成状态显示文本
  TEXT_ABNORMAL: '异常'     // 异常状态显示文本
}
