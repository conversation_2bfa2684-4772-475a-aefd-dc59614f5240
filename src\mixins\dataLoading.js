/**
 * 数据加载混入
 * 提供统一的数据加载、消息提示和状态管理功能
 */

import { extractArrayData, createMessageDebouncer, globalLoadingManager } from '@/utils/dataProcessor'

export default {
  data() {
    return {
      // 防重复消息提示器
      $messageDebouncer: null
    }
  },

  created() {
    // 初始化消息防抖器
    this.$messageDebouncer = createMessageDebouncer(this, 1000)
  },

  beforeDestroy() {
    // 清理加载状态
    globalLoadingManager.clear()
  },

  methods: {
    /**
     * 统一的数据加载方法
     * @param {Object} config - 配置对象
     * @param {Function} config.apiCall - API调用函数
     * @param {Function} config.onSuccess - 成功回调 (data, total) => void
     * @param {Function} config.onError - 错误回调 (error) => void
     * @param {Object} config.loading - 加载配置
     * @param {string} config.loading.key - 加载状态键名
     * @param {string} config.loading.message - 加载提示消息
     * @param {boolean} config.loading.silent - 是否静默加载
     * @param {Object} config.message - 消息配置
     * @param {boolean} config.message.showSuccess - 是否显示成功消息
     * @param {boolean} config.message.showError - 是否显示错误消息
     * @param {string} config.message.successTemplate - 成功消息模板
     * @param {string} config.message.errorTemplate - 错误消息模板
     * @param {boolean} config.message.useDebounce - 是否使用防抖消息
     * @param {Object} config.extract - 数据提取配置
     * @param {string} config.extract.dataKey - 数据字段名
     * @param {boolean} config.extract.allowDirectArray - 是否允许直接数组
     */
    async $loadData(config) {
      const {
        apiCall,
        onSuccess,
        onError,
        loading = {},
        message = {},
        extract = {}
      } = config

      const {
        key = 'default',
        message: loadingMessage = '加载中...',
        silent = false
      } = loading

      const {
        showSuccess = true,
        showError = true,
        successTemplate = '成功加载 {count} 条数据',
        errorTemplate = '数据加载失败: {error}',
        useDebounce = false
      } = message

      const {
        dataKey = 'items',
        allowDirectArray = true
      } = extract

      // 开始加载
      const canStart = globalLoadingManager.start(key, this, loadingMessage, silent)
      if (!canStart) {
        console.log(`数据加载中，跳过重复请求: ${key}`)
        return { success: false, message: '请求进行中' }
      }

      try {
        // 调用API
        const response = await apiCall()
        
        // 提取数据
        const extracted = extractArrayData(response, { dataKey, allowDirectArray })

        if (extracted.success) {
          // 调用成功回调
          if (onSuccess) {
            onSuccess(extracted.data, extracted.total)
          }

          // 显示成功消息
          if (showSuccess) {
            const successMsg = successTemplate.replace('{count}', extracted.data.length)
            if (extracted.data.length > 0) {
              if (useDebounce) {
                this.$messageDebouncer.success(successMsg)
              } else {
                this.$message.success(successMsg)
              }
            } else {
              const noDataMsg = '没有找到数据'
              if (useDebounce) {
                this.$messageDebouncer.info(noDataMsg)
              } else {
                this.$message.info(noDataMsg)
              }
            }
          }

          return { 
            success: true, 
            data: extracted.data, 
            total: extracted.total 
          }
        } else {
          throw new Error(extracted.error)
        }
      } catch (error) {
        console.error(`数据加载失败 [${key}]:`, error)
        
        // 调用错误回调
        if (onError) {
          onError(error)
        }

        // 显示错误消息
        if (showError) {
          const errorMsg = errorTemplate.replace('{error}', error.message || '未知错误')
          if (useDebounce) {
            this.$messageDebouncer.error(errorMsg)
          } else {
            this.$message.error(errorMsg)
          }
        }

        return { 
          success: false, 
          message: error.message || '未知错误',
          error 
        }
      } finally {
        // 结束加载
        globalLoadingManager.finish(key)
      }
    },

    /**
     * 简化的数据加载方法（用于常见场景）
     * @param {Function} apiCall - API调用函数
     * @param {Function} successCallback - 成功回调
     * @param {Object} options - 选项
     */
    async $simpleLoad(apiCall, successCallback, options = {}) {
      const {
        silent = false,
        showMessage = true,
        dataKey = 'items',
        loadingKey = 'default',
        successTemplate = '成功加载 {count} 条数据'
      } = options

      return this.$loadData({
        apiCall,
        onSuccess: successCallback,
        loading: {
          key: loadingKey,
          silent
        },
        message: {
          showSuccess: showMessage,
          successTemplate,
          useDebounce: true // 默认使用防抖
        },
        extract: {
          dataKey
        }
      })
    },

    /**
     * 检查是否正在加载
     * @param {string} key - 加载状态键名
     */
    $isLoading(key = 'default') {
      return globalLoadingManager.isLoading(key)
    },

    /**
     * 防抖消息提示
     * @param {string} type - 消息类型
     * @param {string} message - 消息内容
     * @param {string} key - 消息键名（用于防抖）
     */
    $debouncedMessage(type, message, key = message) {
      if (this.$messageDebouncer) {
        this.$messageDebouncer[type](message, key)
      }
    },

    /**
     * 立即显示消息（不防抖）
     * @param {string} type - 消息类型
     * @param {string} message - 消息内容
     */
    $immediateMessage(type, message) {
      if (this.$messageDebouncer) {
        this.$messageDebouncer.immediate[type](message)
      }
    }
  }
}
