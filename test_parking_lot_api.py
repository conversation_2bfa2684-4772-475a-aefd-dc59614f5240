#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试停车场API的校区和区域功能
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

def test_constants():
    """测试常量定义"""
    try:
        from app.utils.constants import CampusTypes, AreaTypes
        
        print("=== 校区类型测试 ===")
        print("校区选项:", CampusTypes.OPTIONS)
        print("所有校区值:", CampusTypes.get_all_values())
        print("南湖校区验证:", CampusTypes.is_valid("南湖校区"))
        print("无效校区验证:", CampusTypes.is_valid("无效校区"))
        
        print("\n=== 区域类型测试 ===")
        print("区域选项:", AreaTypes.OPTIONS)
        print("所有区域值:", AreaTypes.get_all_values())
        print("教学楼区验证:", AreaTypes.is_valid("教学楼区"))
        print("无效区域验证:", AreaTypes.is_valid("无效区域"))
        
        return True
    except Exception as e:
        print(f"常量测试失败: {e}")
        return False

def test_parking_lot_creation():
    """测试停车场创建数据"""
    test_data = {
        "name": "南湖校区停车场A",
        "address": "南湖校区教学楼区",
        "total_spaces": 50,
        "campus": "南湖校区",
        "area": "教学楼区",
        "manager": "张三",
        "contact": "13800138000",
        "description": "测试停车场"
    }
    
    print("=== 停车场创建数据测试 ===")
    print("测试数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
    
    # 验证校区和区域
    try:
        from app.utils.constants import CampusTypes, AreaTypes
        
        campus_valid = CampusTypes.is_valid(test_data["campus"])
        area_valid = AreaTypes.is_valid(test_data["area"])
        
        print(f"校区验证结果: {campus_valid}")
        print(f"区域验证结果: {area_valid}")
        
        return campus_valid and area_valid
    except Exception as e:
        print(f"验证失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试停车场API功能...")
    
    # 测试常量定义
    constants_ok = test_constants()
    
    # 测试停车场创建数据
    creation_ok = test_parking_lot_creation()
    
    print(f"\n=== 测试结果 ===")
    print(f"常量定义测试: {'通过' if constants_ok else '失败'}")
    print(f"停车场创建测试: {'通过' if creation_ok else '失败'}")
    
    if constants_ok and creation_ok:
        print("所有测试通过！")
    else:
        print("部分测试失败，请检查代码。")
